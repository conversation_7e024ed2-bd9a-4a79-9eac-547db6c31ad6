#!/usr/bin/env python3
"""
Test script for enhanced LectoAI features
"""

import requests
import json

API_BASE = "http://localhost:5000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE}/health", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_question_generation():
    """Test dynamic question generation"""
    print("\n🎯 Testing question generation endpoint...")
    
    sample_content = """
    Machine Learning Fundamentals
    
    Machine learning is a subset of artificial intelligence that focuses on algorithms 
    that can learn from data. Key concepts include:
    
    1. Supervised Learning: Learning with labeled data
    2. Unsupervised Learning: Finding patterns in unlabeled data  
    3. Neural Networks: Computational models inspired by the brain
    4. Deep Learning: Multi-layer neural networks
    
    Common algorithms include linear regression, decision trees, and support vector machines.
    """
    
    data = {
        "context": sample_content
    }
    
    try:
        response = requests.post(f"{API_BASE}/generate-questions", json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Generated Questions:")
                for i, question in enumerate(result['questions'], 1):
                    print(f"  {i}. {question}")
                return True
            else:
                print(f"Error: {result.get('error')}")
                return False
        else:
            print(f"HTTP Error: {response.text}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_enhanced_ai():
    """Test enhanced AI responses"""
    print("\n🤖 Testing enhanced AI endpoint...")
    
    data = {
        "question": "Explain machine learning in simple terms with examples",
        "context": "Machine learning is a method of data analysis that automates analytical model building."
    }
    
    try:
        response = requests.post(f"{API_BASE}/ask", json=data, timeout=45)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ AI Response received:")
                print(f"Model: {result.get('model', 'unknown')}")
                print(f"Formatted: {result.get('formatted', False)}")
                print(f"Response length: {len(result['answer'])} characters")
                print(f"Preview: {result['answer'][:200]}...")
                return True
            else:
                print(f"Error: {result.get('error')}")
                return False
        else:
            print(f"HTTP Error: {response.text}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Run all enhanced tests"""
    print("🎓 LectoAI Enhanced Features Test Suite")
    print("=" * 50)
    
    # Test health
    health_ok = test_health()
    
    # Test question generation
    questions_ok = test_question_generation()
    
    # Test enhanced AI
    ai_ok = test_enhanced_ai()
    
    print("\n" + "=" * 50)
    print("📊 Enhanced Test Results:")
    print(f"✅ Health Check: {'PASS' if health_ok else 'FAIL'}")
    print(f"🎯 Question Generation: {'PASS' if questions_ok else 'FAIL'}")
    print(f"🤖 Enhanced AI: {'PASS' if ai_ok else 'FAIL'}")
    
    if health_ok and questions_ok and ai_ok:
        print("\n🎉 All enhanced features working! Your LectoAI is fully upgraded!")
        print("\n🚀 New Features Available:")
        print("  • Markdown rendering with syntax highlighting")
        print("  • Dynamic question generation based on content")
        print("  • Enhanced AI responses with better formatting")
        print("  • Improved error handling and retry mechanisms")
        print("  • Better performance and loading indicators")
    else:
        print("\n⚠️  Some enhanced features failed. Check the backend server and API key.")

if __name__ == "__main__":
    main()
