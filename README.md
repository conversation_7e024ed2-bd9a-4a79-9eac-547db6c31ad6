# 🎓 LectoAI - AI Learning Assistant

An intelligent lecture notes assistant powered by DeepSeek R1 AI that helps you understand and interact with your educational materials.

## ✨ Features

- 📄 **Multi-format Support**: Upload PDF, DOCX, PPTX, and image files
- 🤖 **AI-Powered Q&A**: Ask questions about your lecture content
- 🎨 **Modern Dark UI**: Professional, responsive interface
- 📱 **Mobile Friendly**: Works on desktop and mobile devices
- 🔍 **OCR Support**: Extract text from images using Tesseract
- ⚡ **Real-time Processing**: Fast file processing and AI responses

## 🚀 Quick Start

### Option 1: Simple Run (Recommended)
```bash
python run.py
```

### Option 2: Manual Setup
1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start Backend**
   ```bash
   cd backend
   python app.py
   ```

3. **Open Frontend**
   - Open `frontend/index.html` in your browser
   - Or visit: `file:///path/to/frontend/index.html`

## 📋 Requirements

- Python 3.7+
- Internet connection (for AI API)
- Tesseract OCR (for image processing)

### Installing Tesseract
- **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt-get install tesseract-ocr`

## 🔧 Configuration

1. Copy `.env.example` to `.env`
2. Update your API key if needed (already configured)
3. Adjust settings as required

## 📁 Project Structure

```
AI Learning Assistant/
├── backend/
│   └── app.py              # Flask backend server
├── frontend/
│   └── index.html          # Web interface
├── requirements.txt        # Python dependencies
├── run.py                 # Simple startup script
├── .env.example           # Environment configuration
└── README.md              # This file
```

## 🎯 Usage

1. **Upload Files**: Drag and drop or click to upload lecture materials
2. **Ask Questions**: Type questions about your uploaded content
3. **Get AI Responses**: Receive detailed explanations and summaries
4. **Interactive Learning**: Continue the conversation to deepen understanding

## 🔑 API Integration

This application uses DeepSeek R1 via OpenRouter with your provided API key:
- **Model**: `deepseek/deepseek-r1`
- **Endpoint**: OpenRouter API
- **Features**: Context-aware responses, educational focus

## 🛠️ Troubleshooting

### Common Issues

1. **Dependencies Missing**
   ```bash
   pip install -r requirements.txt
   ```

2. **Tesseract Not Found**
   - Install Tesseract OCR
   - Add to system PATH
   - Set `TESSERACT_CMD` in `.env` if needed

3. **API Errors**
   - Check internet connection
   - Verify API key is correct
   - Check OpenRouter service status

4. **File Upload Issues**
   - Ensure file size < 10MB
   - Check file format is supported
   - Verify backend server is running

### Backend Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `POST /upload` - File upload and text extraction
- `POST /ask` - AI question answering

## 🎨 Features in Detail

### File Processing
- **PDF**: Text extraction with PyMuPDF
- **DOCX**: Document and table text extraction
- **PPTX**: Slide content extraction
- **Images**: OCR text recognition

### AI Capabilities
- Context-aware responses
- Educational explanations
- Concept clarification
- Content summarization
- Interactive Q&A

## 📝 License

This project is for educational purposes. Please ensure compliance with your institution's academic integrity policies.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Happy Learning! 🎓✨**
