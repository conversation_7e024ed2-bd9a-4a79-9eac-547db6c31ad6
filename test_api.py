#!/usr/bin/env python3
"""
Test script for LectoAI API functionality
"""

import requests
import json

API_BASE = "http://localhost:5000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{API_BASE}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def test_ai_question():
    """Test AI question endpoint"""
    print("\n🤖 Testing AI question endpoint...")
    
    data = {
        "question": "What is machine learning?",
        "context": "Machine learning is a subset of artificial intelligence that focuses on algorithms that can learn from data."
    }
    
    response = requests.post(f"{API_BASE}/ask", json=data)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"AI Response: {result['answer'][:200]}...")
            return True
        else:
            print(f"Error: {result.get('error')}")
            return False
    else:
        print(f"HTTP Error: {response.text}")
        return False

def main():
    """Run all tests"""
    print("🎓 LectoAI API Test Suite")
    print("=" * 40)
    
    # Test health
    health_ok = test_health()
    
    # Test AI functionality
    ai_ok = test_ai_question()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"✅ Health Check: {'PASS' if health_ok else 'FAIL'}")
    print(f"🤖 AI Question: {'PASS' if ai_ok else 'FAIL'}")
    
    if health_ok and ai_ok:
        print("\n🎉 All tests passed! Your LectoAI is ready to use!")
    else:
        print("\n⚠️  Some tests failed. Check the backend server and API key.")

if __name__ == "__main__":
    main()
