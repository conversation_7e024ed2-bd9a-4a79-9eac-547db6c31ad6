# app.py
from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
import os
import tempfile
import fitz  # PyMuPDF
from pptx import Presentation
from docx import Document
import pytesseract
from PIL import Image
import requests
import json
import time
import re
import hashlib
import threading
from datetime import datetime, timedelta
from collections import defaultdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# DeepSeek-R1 API configuration via OpenRouter
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_API_KEY = "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"

# Performance optimizations
response_cache = {}
conversation_memory = defaultdict(list)
session_lock = threading.Lock()

# Connection pooling for better performance
session = requests.Session()
session.headers.update({
    "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
    "Content-Type": "application/json",
    "HTTP-Referer": "http://localhost:5000",
    "X-Title": "LectoAI Learning Assistant"
})

def extract_text_from_file(file_path, filename):
    """Extract text from various file formats"""
    text = ""

    try:
        if filename.lower().endswith('.pdf'):
            # PDF processing
            doc = fitz.open(file_path)
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()

        elif filename.lower().endswith('.pptx'):
            # PowerPoint processing
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text += shape.text + "\n"

        elif filename.lower().endswith('.docx'):
            # DOCX processing
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"

        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            # Image OCR processing
            img = Image.open(file_path)
            text = pytesseract.image_to_string(img)

    except Exception as e:
        print(f"Error extracting text from {filename}: {str(e)}")
        text = f"Error processing file: {str(e)}"

    return text.strip()

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and text extraction"""
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Check file extension
    allowed_extensions = {'.pdf', '.docx', '.pptx', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    file_ext = os.path.splitext(file.filename.lower())[1]

    if file_ext not in allowed_extensions:
        return jsonify({"error": f"Unsupported file type: {file_ext}"}), 400

    try:
        # Save temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            file.save(temp_file.name)
            extracted_text = extract_text_from_file(temp_file.name, file.filename)

        os.unlink(temp_file.name)  # Clean up

        if not extracted_text or extracted_text.startswith("Error processing"):
            return jsonify({"error": "Could not extract text from file"}), 400

        return jsonify({
            "success": True,
            "filename": file.filename,
            "text": extracted_text,
            "text_length": len(extracted_text),
            "preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text
        })

    except Exception as e:
        return jsonify({"error": f"Upload failed: {str(e)}"}), 500

def create_conversational_prompt(question, context, session_id):
    """Create conversational prompt with memory for ChatGPT-like behavior"""

    # Get conversation history
    with session_lock:
        conversation_history = conversation_memory[session_id][-10:]  # Last 5 exchanges

    if context:
        # Analyze context to determine content type
        context_lower = context.lower()

        if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function', 'variable']):
            content_type = "computer science/programming"
        elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof', 'mathematics']):
            content_type = "mathematics"
        elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study', 'analysis']):
            content_type = "science/research"
        elif any(word in context_lower for word in ['history', 'historical', 'century', 'period', 'era']):
            content_type = "history"
        else:
            content_type = "general academic"

        system_prompt = f"""You are an expert educational AI assistant specializing in {content_type}. You engage in natural, conversational dialogue while providing comprehensive educational responses.

CONVERSATION STYLE:
- Be conversational and engaging, like ChatGPT
- Reference previous parts of our conversation when relevant
- Ask follow-up questions to deepen understanding
- Provide examples and analogies to clarify concepts
- Adapt your explanation style based on the user's apparent knowledge level

LECTURE CONTENT:
{context[:2000]}{"..." if len(context) > 2000 else ""}

RESPONSE GUIDELINES:
1. Provide detailed, educational explanations
2. Use clear markdown formatting for better readability
3. Include examples when relevant
4. Break down complex concepts step-by-step
5. Reference our previous conversation when applicable
6. Ask clarifying questions if the query is ambiguous
7. Suggest related topics or deeper questions
8. Use proper structure with headers and bullet points"""

        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation history
        messages.extend(conversation_history)

        # Add current question
        messages.append({"role": "user", "content": question})

    else:
        system_prompt = """You are an expert educational AI assistant. You engage in natural, conversational dialogue while providing comprehensive educational responses.

CONVERSATION STYLE:
- Be conversational and engaging, like ChatGPT
- Reference previous parts of our conversation when relevant
- Ask follow-up questions to deepen understanding
- Provide examples and analogies to clarify concepts
- Adapt your explanation style based on the user's apparent knowledge level

RESPONSE GUIDELINES:
1. Give detailed, educational explanations
2. Use clear markdown formatting
3. Include examples and analogies
4. Break down complex topics step-by-step
5. Reference our previous conversation when applicable
6. Ask clarifying questions if needed
7. Suggest related topics for further exploration
8. Make responses engaging and easy to understand"""

        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation history
        messages.extend(conversation_history)

        # Add current question
        messages.append({"role": "user", "content": question})

    return messages

def create_enhanced_prompt(question, context, context_type="general"):
    """Create an enhanced prompt for better AI responses"""

    if context:
        # Analyze context to determine content type
        context_lower = context.lower()

        if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function', 'variable']):
            content_type = "computer science/programming"
        elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof', 'mathematics']):
            content_type = "mathematics"
        elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study', 'analysis']):
            content_type = "science/research"
        elif any(word in context_lower for word in ['history', 'historical', 'century', 'period', 'era']):
            content_type = "history"
        else:
            content_type = "general academic"

        system_prompt = f"""You are an expert educational AI assistant specializing in {content_type}. Your role is to provide comprehensive, well-structured educational responses.

LECTURE CONTENT:
{context[:3000]}{"..." if len(context) > 3000 else ""}

RESPONSE GUIDELINES:
1. Provide detailed, educational explanations
2. Use clear structure with headers and bullet points
3. Include examples when relevant
4. Break down complex concepts step-by-step
5. Use markdown formatting for better readability
6. If discussing code, use proper syntax highlighting
7. For mathematical content, explain formulas clearly
8. Always relate back to the provided lecture content when possible

Format your response using markdown with:
- **Bold** for key terms
- `code blocks` for technical terms
- ## Headers for main sections
- • Bullet points for lists
- Tables when organizing information
- > Blockquotes for important notes"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Based on the lecture content provided, please answer this question in detail: {question}"}
        ]
    else:
        system_prompt = """You are an expert educational AI assistant. Provide comprehensive, well-structured responses that help users learn effectively.

RESPONSE GUIDELINES:
1. Give detailed, educational explanations
2. Use clear markdown formatting
3. Include examples and analogies
4. Break down complex topics step-by-step
5. Use proper structure with headers and bullet points
6. Make responses engaging and easy to understand"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question}
        ]

    return messages

@app.route('/ask', methods=['POST'])
def ask_question():
    """Handle user questions using DeepSeek-R1 API via OpenRouter with optimizations"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    try:
        # Create enhanced prompt
        messages = create_enhanced_prompt(question, context)

        # Optimized API call with retry mechanism
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000,  # Increased for more detailed responses
            "stream": False,
            "top_p": 0.9,
            "frequency_penalty": 0.1
        }

        # Retry mechanism
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                response = requests.post(
                    OPENROUTER_API_URL,
                    json=payload,
                    headers=headers,
                    timeout=45  # Increased timeout
                )

                if response.status_code == 200:
                    break
                elif response.status_code == 429:  # Rate limit
                    if attempt < max_retries:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue

            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    continue
                raise

        if response.status_code != 200:
            error_msg = f"API Error {response.status_code}: {response.text}"
            print(error_msg)
            return jsonify({"error": "AI service temporarily unavailable", "retry": True}), 503

        response_data = response.json()

        # Extract AI response
        if 'choices' in response_data and len(response_data['choices']) > 0:
            ai_response = response_data['choices'][0]['message']['content']

            return jsonify({
                "success": True,
                "answer": ai_response,
                "model": "deepseek-r1",
                "formatted": True  # Indicates response is markdown formatted
            })
        else:
            return jsonify({"error": "Invalid response from AI service"}), 500

    except requests.exceptions.Timeout:
        return jsonify({"error": "Request timeout - please try again", "retry": True}), 408
    except requests.exceptions.RequestException as e:
        return jsonify({"error": f"Network error: {str(e)}", "retry": True}), 503
    except Exception as e:
        print(f"Error in ask_question: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/ask-stream', methods=['POST'])
def ask_question_stream():
    """Handle user questions with streaming responses for ChatGPT-like experience"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')
    session_id = data.get('session_id', 'default')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    def generate_stream():
        try:
            # Add to conversation memory
            with session_lock:
                conversation_memory[session_id].append({"role": "user", "content": question})
                # Keep only last 10 exchanges to manage memory
                if len(conversation_memory[session_id]) > 20:
                    conversation_memory[session_id] = conversation_memory[session_id][-20:]

            # Check cache first for faster responses
            cache_key = hashlib.md5(f"{question}:{context[:500]}".encode()).hexdigest()
            if cache_key in response_cache:
                cached_response = response_cache[cache_key]
                # Stream cached response word by word for consistency
                words = cached_response.split()
                for word in words:
                    yield f"data: {json.dumps({'token': word + ' ', 'done': False})}\n\n"
                    time.sleep(0.05)  # Simulate streaming
                yield f"data: {json.dumps({'token': '', 'done': True, 'cached': True})}\n\n"
                return

            # Create enhanced prompt with conversation memory
            messages = create_conversational_prompt(question, context, session_id)

            payload = {
                "model": "deepseek/deepseek-r1",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 2000,
                "stream": True,  # Enable streaming
                "top_p": 0.9,
                "frequency_penalty": 0.1
            }

            # Make streaming request
            response = session.post(OPENROUTER_API_URL, json=payload, stream=True, timeout=60)

            if response.status_code == 402:
                # Free tier limit reached - provide helpful fallback
                fallback_response = generate_intelligent_fallback(question, context)
                words = fallback_response.split()
                for word in words:
                    yield f"data: {json.dumps({'token': word + ' ', 'done': False})}\n\n"
                    time.sleep(0.08)  # Simulate streaming
                yield f"data: {json.dumps({'token': '', 'done': True, 'fallback': True})}\n\n"
                return
            elif response.status_code != 200:
                yield f"data: {json.dumps({'error': f'API Error {response.status_code}', 'done': True})}\n\n"
                return

            full_response = ""

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]  # Remove 'data: ' prefix
                            if data_str.strip() == '[DONE]':
                                break

                            chunk_data = json.loads(data_str)
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    token = delta['content']
                                    full_response += token
                                    yield f"data: {json.dumps({'token': token, 'done': False})}\n\n"
                        except json.JSONDecodeError:
                            continue

            # Cache the response for future use
            if full_response:
                response_cache[cache_key] = full_response
                # Add to conversation memory
                with session_lock:
                    conversation_memory[session_id].append({"role": "assistant", "content": full_response})

            yield f"data: {json.dumps({'token': '', 'done': True})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'error': str(e), 'done': True})}\n\n"

    return Response(generate_stream(), mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache', 'Connection': 'keep-alive'})

@app.route('/conversation/<session_id>', methods=['GET'])
def get_conversation(session_id):
    """Get conversation history for a session"""
    with session_lock:
        history = conversation_memory.get(session_id, [])
    return jsonify({
        "success": True,
        "conversation": history,
        "message_count": len(history)
    })

@app.route('/conversation/<session_id>', methods=['DELETE'])
def clear_conversation(session_id):
    """Clear conversation history for a session"""
    with session_lock:
        if session_id in conversation_memory:
            del conversation_memory[session_id]
    return jsonify({
        "success": True,
        "message": "Conversation cleared"
    })

@app.route('/cache/clear', methods=['POST'])
def clear_cache():
    """Clear response cache"""
    global response_cache
    response_cache = {}
    return jsonify({
        "success": True,
        "message": "Cache cleared"
    })

@app.route('/generate-questions', methods=['POST'])
def generate_questions():
    """Generate dynamic questions based on document content"""
    data = request.json
    context = data.get('context', '')

    if not context:
        return jsonify({"error": "No content provided"}), 400

    try:
        # Analyze content and generate relevant questions
        content_preview = context[:1500]  # Use first 1500 chars for analysis

        system_prompt = """You are an educational content analyzer. Based on the provided text, generate 5-7 specific, thoughtful questions that would help a student understand and engage with this material.

REQUIREMENTS:
1. Questions should be directly related to the content
2. Mix different types: factual, analytical, and conceptual
3. Make questions educational and thought-provoking
4. Avoid generic questions - be specific to the content
5. Return ONLY a JSON array of questions, no other text

Example format: ["What is the main concept discussed?", "How does X relate to Y?", "What are the key differences between A and B?"]"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate specific questions for this content:\n\n{content_preview}"}
        ]

        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1",
            "messages": messages,
            "temperature": 0.8,
            "max_tokens": 500,
            "stream": False
        }

        response = requests.post(OPENROUTER_API_URL, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_response = response_data['choices'][0]['message']['content']

                # Try to parse JSON response
                try:
                    # Extract JSON array from response
                    import json
                    # Look for JSON array pattern
                    json_match = re.search(r'\[.*?\]', ai_response, re.DOTALL)
                    if json_match:
                        questions = json.loads(json_match.group())
                    else:
                        # Fallback: split by lines and clean
                        lines = ai_response.strip().split('\n')
                        questions = [line.strip(' "•-1234567890.') for line in lines if line.strip() and '?' in line][:6]

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]  # Limit to 6 questions
                    })

                except (json.JSONDecodeError, AttributeError):
                    # Fallback to manual parsing
                    lines = ai_response.strip().split('\n')
                    questions = []
                    for line in lines:
                        line = line.strip(' "•-1234567890.')
                        if line and '?' in line and len(line) > 10:
                            questions.append(line)

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]
                    })

        # Fallback questions based on content analysis
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

    except Exception as e:
        print(f"Error generating questions: {str(e)}")
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

def generate_fallback_questions(context):
    """Generate fallback questions based on content analysis"""
    context_lower = context.lower()
    questions = []

    # Analyze content type and generate relevant questions
    if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function']):
        questions = [
            "What are the main algorithms or programming concepts discussed?",
            "How do these programming techniques work?",
            "What are the time and space complexities involved?",
            "Can you provide examples of how to implement these concepts?",
            "What are the practical applications of these programming methods?"
        ]
    elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof']):
        questions = [
            "What are the key mathematical concepts presented?",
            "How are these formulas derived?",
            "What are the practical applications of these equations?",
            "Can you explain the proof or reasoning behind these theorems?",
            "What are some examples of solving problems using these methods?"
        ]
    elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study']):
        questions = [
            "What is the main hypothesis or research question?",
            "What methodology was used in this study?",
            "What were the key findings or results?",
            "What are the implications of these research findings?",
            "How does this research relate to other studies in the field?"
        ]
    else:
        # Generic educational questions
        questions = [
            "What are the main concepts or topics covered in this material?",
            "Can you explain the key points in more detail?",
            "What are some real-world applications of these concepts?",
            "How do these ideas relate to other topics in the field?",
            "What are the most important takeaways from this content?"
        ]

    return questions[:5]

def generate_intelligent_fallback(question, context=""):
    """Generate intelligent fallback responses when API is unavailable"""
    question_lower = question.lower()

    # Educational responses based on common topics
    if any(word in question_lower for word in ['neural network', 'machine learning', 'ai', 'artificial intelligence']):
        return """## Neural Networks and Machine Learning

**Neural networks** are computational models inspired by the human brain. Here's a simplified explanation:

### How Neural Networks Work:
1. **Input Layer**: Receives data (like pixels in an image)
2. **Hidden Layers**: Process and transform the data through mathematical operations
3. **Output Layer**: Produces the final result or prediction

### Key Concepts:
- **Neurons**: Basic processing units that receive inputs and produce outputs
- **Weights**: Parameters that determine the strength of connections between neurons
- **Training**: The process of adjusting weights based on examples to improve accuracy

### Real-World Applications:
- Image recognition (identifying objects in photos)
- Natural language processing (understanding text)
- Recommendation systems (suggesting movies or products)
- Medical diagnosis assistance

### Simple Analogy:
Think of a neural network like learning to recognize your friend's face. At first, you might focus on obvious features like hair color. Over time, you learn to recognize subtle details like the shape of their eyes or smile. Neural networks learn similarly, starting with basic patterns and gradually understanding complex relationships.

*Note: This is a simplified explanation. For more detailed information, consider uploading relevant course materials for personalized responses.*"""

    elif any(word in question_lower for word in ['algorithm', 'programming', 'code', 'function']):
        return """## Programming and Algorithms

### What are Algorithms?
An **algorithm** is a step-by-step procedure for solving a problem or completing a task.

### Key Characteristics:
- **Clear Instructions**: Each step must be unambiguous
- **Finite**: Must eventually terminate
- **Effective**: Each step must be doable
- **Input/Output**: Takes input and produces output

### Common Algorithm Types:
1. **Sorting Algorithms**: Organize data in order
   - Bubble Sort, Quick Sort, Merge Sort
2. **Search Algorithms**: Find specific items
   - Linear Search, Binary Search
3. **Graph Algorithms**: Work with connected data
   - Shortest path, network analysis

### Programming Best Practices:
- Write clear, readable code
- Use meaningful variable names
- Comment your code
- Test thoroughly
- Consider efficiency (time and space complexity)

### Example - Simple Search Algorithm:
```python
def linear_search(list, target):
    for i in range(len(list)):
        if list[i] == target:
            return i
    return -1
```

*For specific programming questions about your coursework, please upload your materials for tailored assistance.*"""

    elif any(word in question_lower for word in ['math', 'equation', 'formula', 'calculus', 'algebra']):
        return """## Mathematics Fundamentals

### Problem-Solving Approach:
1. **Understand**: Read the problem carefully
2. **Plan**: Identify what you know and what you need to find
3. **Solve**: Apply appropriate methods and formulas
4. **Check**: Verify your answer makes sense

### Key Mathematical Concepts:
- **Variables**: Symbols representing unknown values
- **Functions**: Relationships between inputs and outputs
- **Equations**: Mathematical statements showing equality
- **Derivatives**: Rate of change (calculus)
- **Integrals**: Area under curves (calculus)

### Study Tips:
- Practice regularly with varied problems
- Understand concepts, don't just memorize formulas
- Work through examples step-by-step
- Seek help when stuck on concepts

### Common Formulas:
- **Quadratic Formula**: x = (-b ± √(b²-4ac)) / 2a
- **Distance Formula**: d = √((x₂-x₁)² + (y₂-y₁)²)
- **Area of Circle**: A = πr²

*For specific mathematical problems from your coursework, please upload your materials for detailed, step-by-step solutions.*"""

    else:
        # General educational response
        return f"""## Educational Response

Thank you for your question: "{question}"

### General Learning Approach:
1. **Break Down Complex Topics**: Divide difficult subjects into smaller, manageable parts
2. **Active Learning**: Engage with the material through practice and application
3. **Connect Concepts**: Link new information to what you already know
4. **Regular Review**: Revisit topics periodically to reinforce understanding

### Study Strategies:
- **Summarize**: Write key points in your own words
- **Teach Others**: Explaining concepts helps solidify understanding
- **Practice Problems**: Apply knowledge through exercises
- **Ask Questions**: Don't hesitate to seek clarification

### Context-Specific Help:
{f"Based on your context about {context[:100]}..." if context else "For more specific assistance tailored to your coursework,"}
please upload your lecture materials, textbooks, or assignment details. This will allow me to provide:
- Detailed explanations specific to your curriculum
- Step-by-step problem solutions
- Relevant examples and analogies
- Practice questions tailored to your level

### Next Steps:
1. Upload your course materials for personalized help
2. Ask specific questions about concepts you're struggling with
3. Request practice problems or examples for better understanding

*This response is generated in offline mode. Upload your materials for AI-powered, personalized assistance.*"""

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "LectoAI Backend",
        "version": "1.0.0"
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "LectoAI Backend API",
        "endpoints": {
            "upload": "/upload (POST)",
            "ask": "/ask (POST)",
            "health": "/health (GET)"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting LectoAI Backend Server...")
    print("📚 File upload endpoint: http://localhost:5000/upload")
    print("🤖 AI chat endpoint: http://localhost:5000/ask")
    print("❤️ Health check: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=True)