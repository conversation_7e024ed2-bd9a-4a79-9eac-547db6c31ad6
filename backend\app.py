# app.py
from flask import Flask, request, jsonify
import os
import tempfile
import fitz  # PyMuPDF
from pptx import Presentation
import pytesseract
from PIL import Image
import requests

app = Flask(__name__)

# DeepSeek-R1 API configuration
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
DEEPSEEK_API_KEY = "your_free_api_key"  # Replace with actual key

def extract_text_from_file(file_path, filename):
    """Extract text from various file formats"""
    text = ""
    
    if filename.endswith('.pdf'):
        # PDF processing
        doc = fitz.open(file_path)
        for page in doc:
            text += page.get_text()
            
    elif filename.endswith('.pptx'):
        # PowerPoint processing
        prs = Presentation(file_path)
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text += shape.text + "\n"
    
    elif filename.endswith(('.png', '.jpg', '.jpeg')):
        # Image OCR processing
        img = Image.open(file_path)
        text = pytesseract.image_to_string(img)
    
    # Add DOCX handling using python-docx
    
    return text

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and text extraction"""
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400
        
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
        
    try:
        # Save temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file.save(temp_file.name)
            extracted_text = extract_text_from_file(temp_file.name, file.filename)
            
        os.unlink(temp_file.name)  # Clean up
        
        return jsonify({
            "filename": file.filename,
            "text": extracted_text[:5000]  # First 5000 chars
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/ask', methods=['POST'])
def ask_question():
    """Handle user questions using DeepSeek-R1 API"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')
    
    if not question:
        return jsonify({"error": "No question provided"}), 400
        
    try:
        # Prepare prompt with context
        prompt = f"Lecture content:\n{context}\n\nQuestion: {question}\n\nAnswer:"
        
        # Call DeepSeek-R1 API
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "deepseek-r1",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7
        }
        
        response = requests.post(DEEPSEEK_API_URL, json=payload, headers=headers)
        response_data = response.json()
        
        # Extract AI response
        ai_response = response_data['choices'][0]['message']['content']
        
        return jsonify({
            "answer": ai_response
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)