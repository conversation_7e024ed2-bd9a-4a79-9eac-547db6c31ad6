# app.py
from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
import os
import tempfile
import fitz  # PyMuPDF
from pptx import Presentation
from docx import Document
import pytesseract
from PIL import Image
import requests
import json
import time
import re
import hashlib
import threading
from datetime import datetime, timedelta
from collections import defaultdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# DeepSeek-R1 API configuration via OpenRouter
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_API_KEY = "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"

def extract_text_from_file(file_path, filename):
    """Extract text from various file formats"""
    text = ""

    try:
        if filename.lower().endswith('.pdf'):
            # PDF processing
            doc = fitz.open(file_path)
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()

        elif filename.lower().endswith('.pptx'):
            # PowerPoint processing
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text += shape.text + "\n"

        elif filename.lower().endswith('.docx'):
            # DOCX processing
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"

        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            # Image OCR processing
            img = Image.open(file_path)
            text = pytesseract.image_to_string(img)

    except Exception as e:
        print(f"Error extracting text from {filename}: {str(e)}")
        text = f"Error processing file: {str(e)}"

    return text.strip()

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and text extraction"""
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Check file extension
    allowed_extensions = {'.pdf', '.docx', '.pptx', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    file_ext = os.path.splitext(file.filename.lower())[1]

    if file_ext not in allowed_extensions:
        return jsonify({"error": f"Unsupported file type: {file_ext}"}), 400

    try:
        # Save temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            file.save(temp_file.name)
            extracted_text = extract_text_from_file(temp_file.name, file.filename)

        os.unlink(temp_file.name)  # Clean up

        if not extracted_text or extracted_text.startswith("Error processing"):
            return jsonify({"error": "Could not extract text from file"}), 400

        return jsonify({
            "success": True,
            "filename": file.filename,
            "text": extracted_text,
            "text_length": len(extracted_text),
            "preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text
        })

    except Exception as e:
        return jsonify({"error": f"Upload failed: {str(e)}"}), 500

def create_enhanced_prompt(question, context, context_type="general"):
    """Create an enhanced prompt for better AI responses"""

    if context:
        # Analyze context to determine content type
        context_lower = context.lower()

        if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function', 'variable']):
            content_type = "computer science/programming"
        elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof', 'mathematics']):
            content_type = "mathematics"
        elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study', 'analysis']):
            content_type = "science/research"
        elif any(word in context_lower for word in ['history', 'historical', 'century', 'period', 'era']):
            content_type = "history"
        else:
            content_type = "general academic"

        system_prompt = f"""You are an expert educational AI assistant specializing in {content_type}. Your role is to provide comprehensive, well-structured educational responses.

LECTURE CONTENT:
{context[:3000]}{"..." if len(context) > 3000 else ""}

RESPONSE GUIDELINES:
1. Provide detailed, educational explanations
2. Use clear structure with headers and bullet points
3. Include examples when relevant
4. Break down complex concepts step-by-step
5. Use markdown formatting for better readability
6. If discussing code, use proper syntax highlighting
7. For mathematical content, explain formulas clearly
8. Always relate back to the provided lecture content when possible

Format your response using markdown with:
- **Bold** for key terms
- `code blocks` for technical terms
- ## Headers for main sections
- • Bullet points for lists
- Tables when organizing information
- > Blockquotes for important notes"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Based on the lecture content provided, please answer this question in detail: {question}"}
        ]
    else:
        system_prompt = """You are an expert educational AI assistant. Provide comprehensive, well-structured responses that help users learn effectively.

RESPONSE GUIDELINES:
1. Give detailed, educational explanations
2. Use clear markdown formatting
3. Include examples and analogies
4. Break down complex topics step-by-step
5. Use proper structure with headers and bullet points
6. Make responses engaging and easy to understand"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question}
        ]

    return messages

@app.route('/ask', methods=['POST'])
def ask_question():
    """Handle user questions using DeepSeek-R1 API via OpenRouter with optimizations"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    try:
        # Create enhanced prompt
        messages = create_enhanced_prompt(question, context)

        # Optimized API call with retry mechanism
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000,  # Increased for more detailed responses
            "stream": False,
            "top_p": 0.9,
            "frequency_penalty": 0.1
        }

        # Retry mechanism
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                response = requests.post(
                    OPENROUTER_API_URL,
                    json=payload,
                    headers=headers,
                    timeout=45  # Increased timeout
                )

                if response.status_code == 200:
                    break
                elif response.status_code == 429:  # Rate limit
                    if attempt < max_retries:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue

            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    continue
                raise

        if response.status_code != 200:
            error_msg = f"API Error {response.status_code}: {response.text}"
            print(error_msg)
            return jsonify({"error": "AI service temporarily unavailable", "retry": True}), 503

        response_data = response.json()

        # Extract AI response
        if 'choices' in response_data and len(response_data['choices']) > 0:
            ai_response = response_data['choices'][0]['message']['content']

            return jsonify({
                "success": True,
                "answer": ai_response,
                "model": "deepseek-r1",
                "formatted": True  # Indicates response is markdown formatted
            })
        else:
            return jsonify({"error": "Invalid response from AI service"}), 500

    except requests.exceptions.Timeout:
        return jsonify({"error": "Request timeout - please try again", "retry": True}), 408
    except requests.exceptions.RequestException as e:
        return jsonify({"error": f"Network error: {str(e)}", "retry": True}), 503
    except Exception as e:
        print(f"Error in ask_question: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/generate-questions', methods=['POST'])
def generate_questions():
    """Generate dynamic questions based on document content"""
    data = request.json
    context = data.get('context', '')

    if not context:
        return jsonify({"error": "No content provided"}), 400

    try:
        # Analyze content and generate relevant questions
        content_preview = context[:1500]  # Use first 1500 chars for analysis

        system_prompt = """You are an educational content analyzer. Based on the provided text, generate 5-7 specific, thoughtful questions that would help a student understand and engage with this material.

REQUIREMENTS:
1. Questions should be directly related to the content
2. Mix different types: factual, analytical, and conceptual
3. Make questions educational and thought-provoking
4. Avoid generic questions - be specific to the content
5. Return ONLY a JSON array of questions, no other text

Example format: ["What is the main concept discussed?", "How does X relate to Y?", "What are the key differences between A and B?"]"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate specific questions for this content:\n\n{content_preview}"}
        ]

        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1",
            "messages": messages,
            "temperature": 0.8,
            "max_tokens": 500,
            "stream": False
        }

        response = requests.post(OPENROUTER_API_URL, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_response = response_data['choices'][0]['message']['content']

                # Try to parse JSON response
                try:
                    # Extract JSON array from response
                    import json
                    # Look for JSON array pattern
                    json_match = re.search(r'\[.*?\]', ai_response, re.DOTALL)
                    if json_match:
                        questions = json.loads(json_match.group())
                    else:
                        # Fallback: split by lines and clean
                        lines = ai_response.strip().split('\n')
                        questions = [line.strip(' "•-1234567890.') for line in lines if line.strip() and '?' in line][:6]

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]  # Limit to 6 questions
                    })

                except (json.JSONDecodeError, AttributeError):
                    # Fallback to manual parsing
                    lines = ai_response.strip().split('\n')
                    questions = []
                    for line in lines:
                        line = line.strip(' "•-1234567890.')
                        if line and '?' in line and len(line) > 10:
                            questions.append(line)

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]
                    })

        # Fallback questions based on content analysis
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

    except Exception as e:
        print(f"Error generating questions: {str(e)}")
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

def generate_fallback_questions(context):
    """Generate fallback questions based on content analysis"""
    context_lower = context.lower()
    questions = []

    # Analyze content type and generate relevant questions
    if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function']):
        questions = [
            "What are the main algorithms or programming concepts discussed?",
            "How do these programming techniques work?",
            "What are the time and space complexities involved?",
            "Can you provide examples of how to implement these concepts?",
            "What are the practical applications of these programming methods?"
        ]
    elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof']):
        questions = [
            "What are the key mathematical concepts presented?",
            "How are these formulas derived?",
            "What are the practical applications of these equations?",
            "Can you explain the proof or reasoning behind these theorems?",
            "What are some examples of solving problems using these methods?"
        ]
    elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study']):
        questions = [
            "What is the main hypothesis or research question?",
            "What methodology was used in this study?",
            "What were the key findings or results?",
            "What are the implications of these research findings?",
            "How does this research relate to other studies in the field?"
        ]
    else:
        # Generic educational questions
        questions = [
            "What are the main concepts or topics covered in this material?",
            "Can you explain the key points in more detail?",
            "What are some real-world applications of these concepts?",
            "How do these ideas relate to other topics in the field?",
            "What are the most important takeaways from this content?"
        ]

    return questions[:5]

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "LectoAI Backend",
        "version": "1.0.0"
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "LectoAI Backend API",
        "endpoints": {
            "upload": "/upload (POST)",
            "ask": "/ask (POST)",
            "health": "/health (GET)"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting LectoAI Backend Server...")
    print("📚 File upload endpoint: http://localhost:5000/upload")
    print("🤖 AI chat endpoint: http://localhost:5000/ask")
    print("❤️ Health check: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=True)