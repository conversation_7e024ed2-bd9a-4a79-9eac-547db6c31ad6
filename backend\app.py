# app.py
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
import fitz  # PyMuPDF
from pptx import Presentation
from docx import Document
import pytesseract
from PIL import Image
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# DeepSeek-R1 API configuration via OpenRouter
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_API_KEY = "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"

def extract_text_from_file(file_path, filename):
    """Extract text from various file formats"""
    text = ""

    try:
        if filename.lower().endswith('.pdf'):
            # PDF processing
            doc = fitz.open(file_path)
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()

        elif filename.lower().endswith('.pptx'):
            # PowerPoint processing
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text += shape.text + "\n"

        elif filename.lower().endswith('.docx'):
            # DOCX processing
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"

        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            # Image OCR processing
            img = Image.open(file_path)
            text = pytesseract.image_to_string(img)

    except Exception as e:
        print(f"Error extracting text from {filename}: {str(e)}")
        text = f"Error processing file: {str(e)}"

    return text.strip()

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and text extraction"""
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Check file extension
    allowed_extensions = {'.pdf', '.docx', '.pptx', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    file_ext = os.path.splitext(file.filename.lower())[1]

    if file_ext not in allowed_extensions:
        return jsonify({"error": f"Unsupported file type: {file_ext}"}), 400

    try:
        # Save temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            file.save(temp_file.name)
            extracted_text = extract_text_from_file(temp_file.name, file.filename)

        os.unlink(temp_file.name)  # Clean up

        if not extracted_text or extracted_text.startswith("Error processing"):
            return jsonify({"error": "Could not extract text from file"}), 400

        return jsonify({
            "success": True,
            "filename": file.filename,
            "text": extracted_text,
            "text_length": len(extracted_text),
            "preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text
        })

    except Exception as e:
        return jsonify({"error": f"Upload failed: {str(e)}"}), 500

@app.route('/ask', methods=['POST'])
def ask_question():
    """Handle user questions using DeepSeek-R1 API via OpenRouter"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    try:
        # Prepare enhanced prompt with context
        if context:
            system_prompt = f"""You are an intelligent lecture assistant. Based on the following lecture content, answer the user's question clearly and comprehensively.

Lecture Content:
{context}

Please provide detailed, educational responses that help the user understand the concepts better."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ]
        else:
            messages = [
                {"role": "system", "content": "You are a helpful educational assistant. Provide clear, detailed explanations to help users learn."},
                {"role": "user", "content": question}
            ]

        # Call DeepSeek-R1 API via OpenRouter
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": False
        }

        response = requests.post(OPENROUTER_API_URL, json=payload, headers=headers, timeout=30)

        if response.status_code != 200:
            error_msg = f"API Error {response.status_code}: {response.text}"
            print(error_msg)
            return jsonify({"error": "AI service temporarily unavailable"}), 503

        response_data = response.json()

        # Extract AI response
        if 'choices' in response_data and len(response_data['choices']) > 0:
            ai_response = response_data['choices'][0]['message']['content']

            return jsonify({
                "success": True,
                "answer": ai_response,
                "model": "deepseek-r1"
            })
        else:
            return jsonify({"error": "Invalid response from AI service"}), 500

    except requests.exceptions.Timeout:
        return jsonify({"error": "Request timeout - please try again"}), 408
    except requests.exceptions.RequestException as e:
        return jsonify({"error": f"Network error: {str(e)}"}), 503
    except Exception as e:
        print(f"Error in ask_question: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "LectoAI Backend",
        "version": "1.0.0"
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "LectoAI Backend API",
        "endpoints": {
            "upload": "/upload (POST)",
            "ask": "/ask (POST)",
            "health": "/health (GET)"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting LectoAI Backend Server...")
    print("📚 File upload endpoint: http://localhost:5000/upload")
    print("🤖 AI chat endpoint: http://localhost:5000/ask")
    print("❤️ Health check: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=True)