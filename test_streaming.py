#!/usr/bin/env python3
"""
Test script for streaming functionality
"""

import requests
import json
import time

API_BASE = "http://localhost:5000"

def test_streaming():
    """Test streaming endpoint"""
    print("🌊 Testing streaming responses...")
    
    data = {
        "question": "Explain how neural networks work in simple terms",
        "context": "Neural networks are computational models inspired by biological neural networks.",
        "session_id": "test_session_123"
    }
    
    try:
        response = requests.post(f"{API_BASE}/ask-stream", json=data, stream=True, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Streaming response:")
            print("-" * 50)
            
            full_response = ""
            token_count = 0
            start_time = time.time()
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]  # Remove 'data: ' prefix
                            chunk_data = json.loads(data_str)
                            
                            if chunk_data.get('error'):
                                print(f"❌ Error: {chunk_data['error']}")
                                return False
                            
                            if chunk_data.get('token'):
                                token = chunk_data['token']
                                full_response += token
                                token_count += 1
                                print(token, end='', flush=True)
                            
                            if chunk_data.get('done'):
                                end_time = time.time()
                                print(f"\n\n✅ Streaming completed!")
                                print(f"📊 Stats:")
                                print(f"  • Total tokens: {token_count}")
                                print(f"  • Response length: {len(full_response)} characters")
                                print(f"  • Time taken: {end_time - start_time:.2f} seconds")
                                print(f"  • Cached: {chunk_data.get('cached', False)}")
                                return True
                                
                        except json.JSONDecodeError as e:
                            print(f"Parse error: {e}")
                            continue
            
            return True
        else:
            print(f"❌ HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_conversation_memory():
    """Test conversation memory"""
    print("\n🧠 Testing conversation memory...")
    
    session_id = "test_memory_session"
    
    # First question
    data1 = {
        "question": "What is Python?",
        "context": "",
        "session_id": session_id
    }
    
    response1 = requests.post(f"{API_BASE}/ask", json=data1, timeout=30)
    if response1.status_code == 200:
        print("✅ First question answered")
    
    # Follow-up question that requires memory
    data2 = {
        "question": "Can you give me an example of what we just discussed?",
        "context": "",
        "session_id": session_id
    }
    
    response2 = requests.post(f"{API_BASE}/ask", json=data2, timeout=30)
    if response2.status_code == 200:
        result = response2.json()
        if result.get('success'):
            print("✅ Follow-up question with memory context answered")
            return True
    
    return False

def test_conversation_endpoints():
    """Test conversation management endpoints"""
    print("\n📝 Testing conversation management...")
    
    session_id = "test_endpoints_session"
    
    # Get conversation (should be empty)
    response = requests.get(f"{API_BASE}/conversation/{session_id}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Empty conversation: {result['message_count']} messages")
    
    # Add a message by asking a question
    data = {
        "question": "Hello, how are you?",
        "session_id": session_id
    }
    requests.post(f"{API_BASE}/ask", json=data, timeout=30)
    
    # Get conversation again
    response = requests.get(f"{API_BASE}/conversation/{session_id}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Conversation with messages: {result['message_count']} messages")
    
    # Clear conversation
    response = requests.delete(f"{API_BASE}/conversation/{session_id}")
    if response.status_code == 200:
        print("✅ Conversation cleared")
        return True
    
    return False

def main():
    """Run streaming tests"""
    print("🌊 LectoAI Streaming & ChatGPT Features Test")
    print("=" * 60)
    
    # Test streaming
    streaming_ok = test_streaming()
    
    # Test conversation memory
    memory_ok = test_conversation_memory()
    
    # Test conversation endpoints
    endpoints_ok = test_conversation_endpoints()
    
    print("\n" + "=" * 60)
    print("📊 ChatGPT-like Features Test Results:")
    print(f"🌊 Streaming Responses: {'PASS' if streaming_ok else 'FAIL'}")
    print(f"🧠 Conversation Memory: {'PASS' if memory_ok else 'FAIL'}")
    print(f"📝 Conversation Management: {'PASS' if endpoints_ok else 'FAIL'}")
    
    if streaming_ok and memory_ok and endpoints_ok:
        print("\n🎉 All ChatGPT-like features working perfectly!")
        print("\n🚀 Your LectoAI now has:")
        print("  • Real-time streaming responses")
        print("  • Conversational memory across sessions")
        print("  • ChatGPT-like user experience")
        print("  • Enhanced response formatting")
        print("  • Copy, share, and feedback features")
    else:
        print("\n⚠️  Some ChatGPT features need attention.")

if __name__ == "__main__":
    main()
