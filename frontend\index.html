<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LectoAI - Smart Lecture Assistant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --sidebar-bg: #202123;
            --chat-bg: #343541;
            --user-bubble: #19c37d;
            --ai-bubble: #444654;
            --text-primary: #ececf1;
            --text-secondary: #8e8ea0;
            --accent: #10a37f;
            --input-bg: #40414f;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--chat-bg);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg);
            padding: 16px;
            display: flex;
            flex-direction: column;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .logo {
            padding: 16px 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #4d4d4f;
        }

        .logo i {
            color: var(--accent);
            font-size: 24px;
        }

        .logo h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .upload-section {
            margin-bottom: 24px;
        }

        .upload-section h2 {
            font-size: 14px;
            margin-bottom: 12px;
            color: var(--text-secondary);
            padding-left: 8px;
        }

        #dropzone {
            border: 2px dashed #4d4d4f;
            border-radius: 8px;
            padding: 24px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background-color: rgba(255, 255, 255, 0.03);
        }

        #dropzone:hover {
            background-color: rgba(255, 255, 255, 0.06);
            border-color: var(--accent);
        }

        #dropzone i {
            font-size: 32px;
            margin-bottom: 12px;
            color: var(--text-secondary);
        }

        #dropzone p {
            font-size: 14px;
            color: var(--text-secondary);
        }

        #fileInput {
            display: none;
        }

        .status-bar {
            margin-top: auto;
            padding: 16px 8px;
            border-top: 1px solid #4d4d4f;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            background-color: #10a37f;
            border-radius: 50%;
        }

        /* Main Chat Area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .chat-header {
            padding: 16px;
            border-bottom: 1px solid #4d4d4f;
            text-align: center;
            font-size: 14px;
            color: var(--text-secondary);
        }

        #chatMessages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .message {
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            display: flex;
            gap: 24px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .user .avatar {
            background-color: var(--user-bubble);
        }

        .ai .avatar {
            background-color: var(--accent);
        }

        .message-content {
            flex: 1;
        }

        .user .message-content {
            color: var(--text-primary);
        }

        .ai .message-content {
            color: var(--text-primary);
        }

        /* Input Area */
        .input-container {
            padding: 16px 24px;
            position: relative;
        }

        .input-box {
            position: relative;
            border-radius: 8px;
            background-color: var(--input-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }

        #userInput {
            width: 100%;
            background: none;
            border: none;
            padding: 16px 48px 16px 16px;
            color: var(--text-primary);
            font-size: 16px;
            resize: none;
            max-height: 200px;
            outline: none;
        }

        #sendBtn {
            position: absolute;
            right: 12px;
            bottom: 12px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #sendBtn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--accent);
        }

        .input-footer {
            text-align: center;
            color: var(--text-secondary);
            font-size: 12px;
            margin-top: 8px;
        }

        /* Scrollbar styling */
        #chatMessages::-webkit-scrollbar {
            width: 8px;
        }

        #chatMessages::-webkit-scrollbar-track {
            background: transparent;
        }

        #chatMessages::-webkit-scrollbar-thumb {
            background: #565869;
            border-radius: 4px;
        }

        /* Loading animation */
        .typing-indicator {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 20px;
        }

        .typing-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(1) { left: 8px; animation-delay: 0s; }
        .typing-dot:nth-child(2) { left: 32px; animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { left: 56px; animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-6px); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
            }
            .sidebar.active {
                transform: translateX(0);
            }
            .mobile-menu-btn {
                display: block;
                position: absolute;
                top: 16px;
                left: 16px;
                z-index: 90;
            }
        }

        .mobile-menu-btn {
            display: none;
            background: var(--input-bg);
            border: none;
            color: var(--text-primary);
            width: 40px;
            height: 40px;
            border-radius: 4px;
            font-size: 20px;
            cursor: pointer;
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 90;
        }
    </style>
</head>
<body>
    <!-- Mobile menu button -->
    <button class="mobile-menu-btn" id="menuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <i class="fas fa-graduation-cap"></i>
            <h1>LectoAI</h1>
        </div>
        
        <div class="upload-section">
            <h2>Upload Lecture Notes</h2>
            <div id="dropzone">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Drop files or click to upload</p>
                <p class="small">(PDF, DOCX, PPT, PNG, JPG)</p>
                <input type="file" id="fileInput" multiple accept=".pdf,.docx,.pptx,.png,.jpg">
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator"></div>
            <span>AI Assistant Ready</span>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-container">
        <div class="chat-header">
            <p>Ask questions about your lecture notes</p>
        </div>
        
        <div id="chatMessages">
            <div class="message ai">
                <div class="avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>Hello! I'm LectoAI, your lecture notes assistant. Upload your files (PDF, DOCX, PPT, images) and I'll help you understand them. Ask me anything about your course materials!</p>
                </div>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-box">
                <textarea 
                    id="userInput" 
                    placeholder="Ask about your lecture notes..." 
                    rows="1"
                ></textarea>
                <button id="sendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                LectoAI can explain concepts, summarize content, and answer questions about your materials
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const dropzone = document.getElementById('dropzone');
            const fileInput = document.getElementById('fileInput');
            const chatMessages = document.getElementById('chatMessages');
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');
            const menuBtn = document.getElementById('menuBtn');
            const sidebar = document.getElementById('sidebar');
            
            // State variables
            let lectureContext = "";
            let isProcessing = false;
            const API_BASE_URL = 'http://localhost:5000';
            
            // Auto-resize textarea
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
            
            // Mobile menu toggle
            menuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });
            
            // File upload handling
            dropzone.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', async (e) => {
                if (e.target.files.length === 0) return;

                const file = e.target.files[0];

                // Validate file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    addMessage("File too large. Please select a file smaller than 10MB.", 'error');
                    return;
                }

                addMessage(`Uploading ${file.name}...`, 'status');

                // Create a loading indicator
                const loadingMsg = addMessage("Processing file...", 'ai', true);

                try {
                    // Create FormData for file upload
                    const formData = new FormData();
                    formData.append('file', file);

                    // Upload to backend
                    const response = await fetch(`${API_BASE_URL}/upload`, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    // Remove loading indicator
                    loadingMsg.remove();

                    if (response.ok && result.success) {
                        // Success message
                        addMessage(`Successfully processed ${file.name}! I've analyzed your lecture notes (${result.text_length} characters extracted). Now you can ask me questions about this material.`, 'ai');

                        // Store the extracted text as context
                        lectureContext = result.text;

                        // Show preview of extracted content
                        if (result.preview) {
                            addMessage(`Preview of extracted content:\n\n"${result.preview}"`, 'ai');
                        }

                        // Add suggested questions
                        setTimeout(() => {
                            addMessage("Here are some questions you might ask:", 'ai');
                            addSuggestedQuestions();
                        }, 500);

                    } else {
                        addMessage(`Upload failed: ${result.error || 'Unknown error'}`, 'error');
                    }

                } catch (error) {
                    loadingMsg.remove();
                    addMessage(`Upload failed: ${error.message}`, 'error');
                }
            });
            
            // Send message functionality
            async function sendMessage() {
                const question = userInput.value.trim();
                if (!question || isProcessing) return;

                // Add user's message to chat
                addMessage(question, 'user');
                userInput.value = '';
                userInput.style.height = 'auto';

                isProcessing = true;

                try {
                    // Add AI typing indicator
                    const aiMessage = addMessage("", 'ai', true);

                    // Call backend API
                    const response = await fetch(`${API_BASE_URL}/ask`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            question: question,
                            context: lectureContext
                        })
                    });

                    const result = await response.json();

                    // Remove typing indicator
                    aiMessage.remove();

                    if (response.ok && result.success) {
                        // Add AI response
                        addMessage(result.answer, 'ai');
                    } else {
                        // Handle API errors
                        const errorMsg = result.error || 'Failed to get AI response';
                        addMessage(`Error: ${errorMsg}`, 'error');

                        // Fallback to demo response if API fails
                        if (response.status === 503 || response.status === 408) {
                            setTimeout(() => {
                                addMessage("🔄 Using offline mode. Here's a basic response:", 'ai');
                                const fallbackResponse = generateDemoResponse(question);
                                addMessage(fallbackResponse, 'ai');
                            }, 1000);
                        }
                    }

                } catch (error) {
                    // Remove typing indicator if still present
                    const typingIndicators = document.querySelectorAll('.typing-indicator');
                    typingIndicators.forEach(indicator => indicator.parentElement.parentElement.remove());

                    addMessage(`Connection error: ${error.message}`, 'error');

                    // Fallback to demo response
                    setTimeout(() => {
                        addMessage("🔄 Using offline mode. Here's a basic response:", 'ai');
                        const fallbackResponse = generateDemoResponse(question);
                        addMessage(fallbackResponse, 'ai');
                    }, 1000);

                } finally {
                    isProcessing = false;
                }
            }
            
            // Send message on button click
            sendBtn.addEventListener('click', sendMessage);
            
            // Send message on Enter (without Shift)
            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Add message to chat UI
            function addMessage(text, sender, isTyping = false) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message', sender);

                // Create avatar
                const avatar = document.createElement('div');
                avatar.classList.add('avatar');

                if (sender === 'user') {
                    avatar.innerHTML = '<i class="fas fa-user"></i>';
                } else if (sender === 'error') {
                    avatar.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    avatar.style.backgroundColor = '#dc3545';
                } else if (sender === 'status') {
                    avatar.innerHTML = '<i class="fas fa-info-circle"></i>';
                    avatar.style.backgroundColor = '#17a2b8';
                } else {
                    avatar.innerHTML = '<i class="fas fa-robot"></i>';
                }

                // Create message content
                const content = document.createElement('div');
                content.classList.add('message-content');

                if (isTyping) {
                    const typingIndicator = document.createElement('div');
                    typingIndicator.classList.add('typing-indicator');
                    typingIndicator.innerHTML = `
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    `;
                    content.appendChild(typingIndicator);
                } else {
                    // Handle multiline text and preserve formatting
                    const formattedText = text.replace(/\n/g, '<br>');
                    content.innerHTML = `<p>${formattedText}</p>`;

                    // Style error messages
                    if (sender === 'error') {
                        content.style.color = '#ff6b6b';
                    } else if (sender === 'status') {
                        content.style.color = '#4ecdc4';
                    }
                }

                // Assemble message
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;

                return messageDiv;
            }
            
            // Generate demo responses based on question
            function generateDemoResponse(question) {
                const lowerQ = question.toLowerCase();
                
                if (lowerQ.includes('sort') || lowerQ.includes('algorithm')) {
                    return "Sorting algorithms are methods for reorganizing data in ascending or descending order. Common algorithms include Bubble Sort (O(n²)), Merge Sort (O(n log n)), and Quick Sort (O(n log n) average case). The choice depends on data size and structure.";
                } else if (lowerQ.includes('big o') || lowerQ.includes('complexity')) {
                    return "Big O notation describes algorithm efficiency in terms of time or space complexity as input size grows. O(1) is constant time, O(n) is linear, O(log n) is logarithmic, and O(n²) is quadratic. We use it to compare algorithm scalability.";
                } else if (lowerQ.includes('recursion') || lowerQ.includes('recursive')) {
                    return "Recursion is when a function calls itself to solve smaller instances of the same problem. It requires a base case to terminate and a recursive case that reduces the problem size. Used in tree traversals, divide-and-conquer algorithms, and factorial calculations.";
                } else if (lowerQ.includes('data struct')) {
                    return "Data structures organize data for efficient access. Arrays offer O(1) access but fixed size. Linked lists have O(n) access but dynamic sizing. Stacks (LIFO) and queues (FIFO) control access order. Trees enable hierarchical data, and hash tables provide key-value storage.";
                } else {
                    return "Based on your lecture notes, here's what I understand: Algorithms are step-by-step procedures for calculations. They're fundamental to computer science for solving problems efficiently. Key aspects include correctness, efficiency (time/space complexity), and optimality. Common algorithm design techniques include divide-and-conquer, dynamic programming, and greedy approaches.";
                }
            }
            
            // Add suggested questions
            function addSuggestedQuestions() {
                const questions = [
                    "Explain sorting algorithms",
                    "What is Big O notation?",
                    "How does recursion work?",
                    "What are the main data structures?",
                    "Summarize the key concepts"
                ];
                
                const suggestionsDiv = document.createElement('div');
                suggestionsDiv.classList.add('message', 'ai');
                suggestionsDiv.style.marginTop = '10px';
                
                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.innerHTML = '<i class="fas fa-robot"></i>';
                
                const content = document.createElement('div');
                content.classList.add('message-content');
                
                const container = document.createElement('div');
                container.style.display = 'flex';
                container.style.flexWrap = 'wrap';
                container.style.gap = '8px';
                container.style.marginTop = '8px';
                
                questions.forEach(q => {
                    const button = document.createElement('button');
                    button.textContent = q;
                    button.style.background = 'rgba(255, 255, 255, 0.1)';
                    button.style.border = '1px solid rgba(255, 255, 255, 0.2)';
                    button.style.borderRadius = '4px';
                    button.style.padding = '6px 12px';
                    button.style.fontSize = '14px';
                    button.style.cursor = 'pointer';
                    button.style.transition = 'all 0.2s';
                    
                    button.addEventListener('mouseenter', () => {
                        button.style.background = 'rgba(16, 163, 127, 0.2)';
                    });
                    
                    button.addEventListener('mouseleave', () => {
                        button.style.background = 'rgba(255, 255, 255, 0.1)';
                    });
                    
                    button.addEventListener('click', () => {
                        userInput.value = q;
                        userInput.dispatchEvent(new Event('input'));
                        sendMessage();
                    });
                    
                    container.appendChild(button);
                });
                
                content.appendChild(container);
                suggestionsDiv.appendChild(avatar);
                suggestionsDiv.appendChild(content);
                chatMessages.appendChild(suggestionsDiv);
                
                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        });
    </script>
</body>
</html>